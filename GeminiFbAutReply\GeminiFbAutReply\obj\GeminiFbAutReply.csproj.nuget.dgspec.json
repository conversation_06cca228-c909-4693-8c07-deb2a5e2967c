{"format": 1, "restore": {"C:\\Git\\GeminiFbAutReply\\GeminiFbAutReply\\GeminiFbAutReply\\GeminiFbAutReply.csproj": {}}, "projects": {"C:\\Git\\GeminiFbAutReply\\GeminiFbAutReply\\GeminiFbAutReply.Client\\GeminiFbAutReply.Client.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Git\\GeminiFbAutReply\\GeminiFbAutReply\\GeminiFbAutReply.Client\\GeminiFbAutReply.Client.csproj", "projectName": "GeminiFbAutReply.Client", "projectPath": "C:\\Git\\GeminiFbAutReply\\GeminiFbAutReply\\GeminiFbAutReply.Client\\GeminiFbAutReply.Client.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Git\\GeminiFbAutReply\\GeminiFbAutReply\\GeminiFbAutReply.Client\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Users\\<USER>\\AppData\\Roaming\\Godot\\mono\\GodotNuGetFallbackFolder", "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\config\\Godot.Offline.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"Microsoft.AspNetCore.Components.WebAssembly": {"target": "Package", "version": "[9.0.1, )"}, "Microsoft.NET.ILLink.Tasks": {"suppressParent": "All", "target": "Package", "version": "[9.0.1, )", "autoReferenced": true}, "Microsoft.NET.Sdk.WebAssembly.Pack": {"suppressParent": "All", "target": "Package", "version": "[9.0.1, )", "autoReferenced": true}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.102/PortableRuntimeIdentifierGraph.json"}}, "runtimes": {"browser-wasm": {"#import": []}}}, "C:\\Git\\GeminiFbAutReply\\GeminiFbAutReply\\GeminiFbAutReply\\GeminiFbAutReply.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Git\\GeminiFbAutReply\\GeminiFbAutReply\\GeminiFbAutReply\\GeminiFbAutReply.csproj", "projectName": "GeminiFbAutReply", "projectPath": "C:\\Git\\GeminiFbAutReply\\GeminiFbAutReply\\GeminiFbAutReply\\GeminiFbAutReply.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Git\\GeminiFbAutReply\\GeminiFbAutReply\\GeminiFbAutReply\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Users\\<USER>\\AppData\\Roaming\\Godot\\mono\\GodotNuGetFallbackFolder", "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\config\\Godot.Offline.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {"C:\\Git\\GeminiFbAutReply\\GeminiFbAutReply\\GeminiFbAutReply.Client\\GeminiFbAutReply.Client.csproj": {"projectPath": "C:\\Git\\GeminiFbAutReply\\GeminiFbAutReply\\GeminiFbAutReply.Client\\GeminiFbAutReply.Client.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"Microsoft.AspNetCore.Components.WebAssembly.Server": {"target": "Package", "version": "[9.0.1, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.102/PortableRuntimeIdentifierGraph.json"}}}}}